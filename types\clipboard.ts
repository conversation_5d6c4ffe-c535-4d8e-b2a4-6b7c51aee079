/**
 * 剪切板历史记录项的数据结构
 */
export interface ClipboardHistoryItem {
  /** 唯一标识符 */
  id: string;
  /** 剪切板内容 */
  content: string;
  /** 创建时间戳 */
  timestamp: number;
  /** 内容类型 */
  type: 'text' | 'image' | 'url';
  /** 内容预览（截取前100个字符） */
  preview: string;
}

/**
 * 剪切板历史记录状态
 */
export interface ClipboardHistoryState {
  /** 历史记录列表 */
  items: ClipboardHistoryItem[];
  /** 是否正在加载 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
}

/**
 * 剪切板历史记录操作类型
 */
export interface ClipboardHistoryActions {
  /** 添加新的剪切板记录 */
  addItem: (content: string) => Promise<void>;
  /** 删除指定记录 */
  removeItem: (id: string) => Promise<void>;
  /** 清空所有记录 */
  clearAll: () => Promise<void>;
  /** 复制指定记录到剪切板 */
  copyItem: (item: ClipboardHistoryItem) => Promise<void>;
  /** 刷新历史记录 */
  refresh: () => Promise<void>;
}

/**
 * 剪切板历史记录配置
 */
export interface ClipboardHistoryConfig {
  /** 最大保存记录数量 */
  maxItems: number;
  /** 是否自动保存剪切板内容 */
  autoSave: boolean;
  /** 内容预览最大长度 */
  previewLength: number;
}
