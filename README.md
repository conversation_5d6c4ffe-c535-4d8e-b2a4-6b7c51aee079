# Best Clipboard

一个使用 React Native + Expo + TypeScript 创建的基础移动应用项目。

## 项目特性

✅ **TypeScript 支持** - 完整的类型安全开发体验  
✅ **Expo Router 导航** - 基于文件系统的现代导航方案  
✅ **跨平台兼容** - 支持 iOS、Android 和 Web 平台  
✅ **现代化开发工具链** - 使用最新的 React Native 和 Expo 版本  
✅ **热重载** - 快速开发和调试体验  
✅ **深色模式支持** - 自动适配系统主题  

## 技术栈

- **React Native** 0.79.5
- **Expo** ~53.0.20
- **TypeScript** ~5.8.3
- **Expo Router** ~5.1.4
- **React Navigation** ^7.1.6

## 快速开始

### 环境要求

- Node.js 18+ 
- npm 或 yarn
- Expo CLI (可选，项目已包含)

### 安装依赖

```bash
cd best-clipboard
npm install
```

### 启动开发服务器

```bash
npm start
```

### 运行应用

#### 在移动设备上运行
1. 安装 Expo Go 应用 ([iOS](https://apps.apple.com/app/expo-go/id982107779) | [Android](https://play.google.com/store/apps/details?id=host.exp.exponent))
2. 扫描终端中显示的二维码

#### 在模拟器上运行

**Android:**
```bash
npm run android
```

**iOS (需要 macOS):**
```bash
npm run ios
```

**Web:**
```bash
npm run web
```

## 项目结构

```
best-clipboard/
├── app/                    # 应用页面 (Expo Router)
│   ├── (tabs)/            # 标签页导航
│   │   ├── index.tsx      # 首页
│   │   ├── two.tsx        # 设置页
│   │   └── _layout.tsx    # 标签页布局
│   ├── _layout.tsx        # 根布局
│   ├── modal.tsx          # 模态页面
│   └── +not-found.tsx     # 404 页面
├── components/            # 可复用组件
├── constants/             # 常量配置
├── assets/               # 静态资源
├── app.json              # Expo 配置
├── tsconfig.json         # TypeScript 配置
└── package.json          # 项目依赖
```

## 开发指南

### 添加新页面
在 `app/` 目录下创建新的 `.tsx` 文件，Expo Router 会自动生成对应的路由。

### 添加新组件
在 `components/` 目录下创建可复用的 React 组件。

### 样式和主题
项目支持深色模式，使用 `@/components/Themed` 中的组件可以自动适配主题。

### TypeScript 配置
项目已配置严格的 TypeScript 检查，确保代码质量和类型安全。

## 构建和部署

### 开发构建
```bash
npx expo build:android
npx expo build:ios
```

### 生产构建
```bash
npx expo build:android --type app-bundle
npx expo build:ios --type archive
```

## 常用命令

- `npm start` - 启动开发服务器
- `npm run android` - 在 Android 设备/模拟器上运行
- `npm run ios` - 在 iOS 设备/模拟器上运行  
- `npm run web` - 在浏览器中运行
- `npm test` - 运行测试

## 了解更多

- [Expo 文档](https://docs.expo.dev/)
- [React Native 文档](https://reactnative.dev/)
- [Expo Router 文档](https://expo.github.io/router/)
- [TypeScript 文档](https://www.typescriptlang.org/)

## 许可证

MIT License
