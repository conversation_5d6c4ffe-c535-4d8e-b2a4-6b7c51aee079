# Expo局域网访问配置完整指南

## 概述

本指南将帮助您配置Expo开发环境，使同一局域网内的其他设备能够访问您的开发服务器进行测试。

## 1. Expo开发服务器配置

### 1.1 基础配置

默认情况下，Expo开发服务器只监听localhost。要启用局域网访问，需要使用以下命令：

```bash
# 启动并监听所有网络接口
npx expo start --tunnel

# 或者使用局域网模式
npx expo start --lan

# 指定主机地址
npx expo start --host 0.0.0.0
```

### 1.2 修改package.json脚本

更新您的package.json文件，添加局域网访问脚本：

```json
{
  "scripts": {
    "start": "expo start",
    "start:lan": "expo start --lan",
    "start:tunnel": "expo start --tunnel",
    "start:host": "expo start --host 0.0.0.0",
    "android": "expo start --android",
    "ios": "expo start --ios",
    "web": "expo start --web"
  }
}
```

### 1.3 Expo配置文件

在项目根目录创建或修改 `.expo/settings.json`：

```json
{
  "hostType": "lan",
  "lanType": "ip",
  "dev": true,
  "minify": false,
  "urlRandomness": null
}
```

## 2. 网络配置步骤

### 2.1 获取本机IP地址

#### Windows系统
```cmd
# 命令提示符
ipconfig

# PowerShell
Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -like "192.168.*"}
```

#### macOS系统
```bash
# 获取所有网络接口
ifconfig | grep "inet " | grep -v 127.0.0.1

# 或者使用更简单的命令
ipconfig getifaddr en0
```

#### Linux系统
```bash
# 获取IP地址
hostname -I

# 或者使用ip命令
ip addr show | grep "inet " | grep -v 127.0.0.1
```

### 2.2 启动局域网模式

```bash
# 方法1：使用LAN模式
npx expo start --lan

# 方法2：使用隧道模式（推荐用于复杂网络环境）
npx expo start --tunnel

# 方法3：指定主机
npx expo start --host *************  # 替换为您的实际IP
```

### 2.3 验证网络配置

启动服务器后，您应该看到类似以下的输出：

```
› Metro waiting on exp://*************:8081
› Scan the QR code above with Expo Go (Android) or the Camera app (iOS)

› Press a │ open Android
› Press i │ open iOS simulator
› Press w │ open web

› Press r │ reload app
› Press m │ toggle menu
› Press d │ show developer menu
› Press shift+d │ toggle development mode
```

## 3. 防火墙设置

### 3.1 Windows防火墙

#### 方法1：通过Windows Defender防火墙界面
1. 打开"Windows Defender 防火墙"
2. 点击"允许应用或功能通过Windows Defender防火墙"
3. 点击"更改设置"
4. 点击"允许其他应用"
5. 浏览并选择Node.js可执行文件
6. 确保勾选"专用"和"公用"网络

#### 方法2：使用命令行（管理员权限）
```cmd
# 允许Node.js通过防火墙
netsh advfirewall firewall add rule name="Node.js" dir=in action=allow program="C:\Program Files\nodejs\node.exe"

# 允许特定端口（Expo默认使用8081）
netsh advfirewall firewall add rule name="Expo Dev Server" dir=in action=allow protocol=TCP localport=8081

# 允许Metro Bundler端口（通常是8080）
netsh advfirewall firewall add rule name="Metro Bundler" dir=in action=allow protocol=TCP localport=8080
```

### 3.2 macOS防火墙

#### 系统偏好设置方法
1. 打开"系统偏好设置" > "安全性与隐私" > "防火墙"
2. 点击锁图标并输入密码
3. 点击"防火墙选项"
4. 点击"+"添加应用程序
5. 选择Node.js或Terminal应用
6. 设置为"允许传入连接"

#### 命令行方法
```bash
# 添加Node.js到防火墙允许列表
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --add /usr/local/bin/node
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --unblockapp /usr/local/bin/node
```

### 3.3 Linux防火墙（UFW）

```bash
# 允许Expo开发服务器端口
sudo ufw allow 8081/tcp
sudo ufw allow 8080/tcp

# 允许特定IP段访问（更安全）
sudo ufw allow from ***********/24 to any port 8081
sudo ufw allow from ***********/24 to any port 8080

# 重新加载防火墙规则
sudo ufw reload
```

## 4. 手机端连接方法

### 4.1 使用Expo Go应用

#### Android设备
1. 从Google Play Store下载"Expo Go"应用
2. 确保手机连接到与开发机相同的WiFi网络
3. 打开Expo Go应用
4. 扫描开发服务器显示的QR码
5. 或者手动输入URL：`exp://*************:8081`

#### iOS设备
1. 从App Store下载"Expo Go"应用
2. 确保iPhone连接到与开发机相同的WiFi网络
3. 使用相机应用扫描QR码，或在Expo Go中扫描
4. 点击通知中的链接打开应用

### 4.2 使用开发构建

如果您使用的是开发构建（Development Build）：

```bash
# 创建开发构建
npx expo install expo-dev-client
npx expo run:android --variant debug
npx expo run:ios --configuration Debug
```

### 4.3 手动连接

如果QR码扫描不工作，可以手动输入连接信息：

1. 在Expo Go中点击"Enter URL manually"
2. 输入：`exp://[您的IP地址]:8081`
3. 例如：`exp://*************:8081`

## 5. 故障排除

### 5.1 常见问题及解决方法

#### 问题1：无法连接到开发服务器
**解决方案：**
```bash
# 检查网络连接
ping *************  # 替换为您的IP

# 检查端口是否被占用
netstat -an | grep 8081  # Windows/Linux
lsof -i :8081  # macOS

# 重启Expo服务器
npx expo start --clear
```

#### 问题2：QR码扫描失败
**解决方案：**
1. 确保手机和电脑在同一WiFi网络
2. 检查防火墙设置
3. 尝试手动输入URL
4. 使用隧道模式：`npx expo start --tunnel`

#### 问题3：端口冲突
**解决方案：**
```bash
# 指定不同端口
npx expo start --port 8082

# 或者杀死占用端口的进程
# Windows
netstat -ano | findstr :8081
taskkill /PID [进程ID] /F

# macOS/Linux
lsof -ti:8081 | xargs kill -9
```

#### 问题4：网络权限问题
**解决方案：**
```bash
# 检查网络接口
ipconfig  # Windows
ifconfig  # macOS/Linux

# 重置网络配置
npx expo start --reset-cache --clear
```

### 5.2 调试工具

#### 网络连接测试
```bash
# 测试端口连通性
telnet ************* 8081

# 使用curl测试
curl http://*************:8081

# 检查Expo服务状态
npx expo doctor
```

#### 日志查看
```bash
# 启用详细日志
npx expo start --verbose

# 查看Metro日志
npx expo start --reset-cache --verbose
```

## 6. 安全考虑

### 6.1 网络安全

1. **仅在可信网络中使用**
   - 避免在公共WiFi中启用局域网访问
   - 使用VPN或专用网络进行远程开发

2. **端口安全**
   ```bash
   # 仅允许特定IP访问
   sudo ufw allow from ***********/24 to any port 8081
   ```

3. **临时访问**
   - 开发完成后及时关闭局域网访问
   - 使用环境变量控制网络模式

### 6.2 开发环境隔离

创建环境配置文件 `.env.local`：

```bash
# 开发环境配置
EXPO_DEV_SERVER_HOST=0.0.0.0
EXPO_DEV_SERVER_PORT=8081
EXPO_USE_LAN=true

# 生产环境配置
EXPO_USE_LAN=false
```

### 6.3 访问控制

在app.json中配置开发模式：

```json
{
  "expo": {
    "extra": {
      "development": {
        "allowLanAccess": true,
        "trustedHosts": ["***********/24"]
      }
    }
  }
}
```

## 7. 高级配置

### 7.1 自定义网络配置

创建 `metro.config.js`：

```javascript
const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// 自定义服务器配置
config.server = {
  ...config.server,
  host: '0.0.0.0',
  port: 8081,
};

module.exports = config;
```

### 7.2 多设备同步测试

```bash
# 启动多个实例
npx expo start --port 8081 --lan  # 主实例
npx expo start --port 8082 --lan  # 测试实例
```

### 7.3 自动化脚本

创建 `scripts/start-lan.js`：

```javascript
const { spawn } = require('child_process');
const os = require('os');

// 获取本机IP
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

const ip = getLocalIP();
console.log(`Starting Expo on ${ip}:8081`);

const expo = spawn('npx', ['expo', 'start', '--lan'], {
  stdio: 'inherit',
  shell: true
});

expo.on('close', (code) => {
  console.log(`Expo process exited with code ${code}`);
});
```

使用脚本：
```bash
node scripts/start-lan.js
```
